<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线搜索功能测试</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .search-container {
            margin-bottom: 20px;
        }
        input {
            width: 300px;
            padding: 10px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .results {
            margin-top: 20px;
        }
        .result-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .result-name {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        .result-type {
            color: #666;
            margin-top: 5px;
        }
        .result-coords {
            color: #888;
            font-size: 14px;
            margin-top: 5px;
        }
        .no-results {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        .loading {
            text-align: center;
            color: #007bff;
            padding: 20px;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .info {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>离线搜索功能测试</h1>
    
    <div class="search-container">
        <input type="text" id="searchInput" placeholder="输入搜索关键词（如：大兴安岭、地区）">
        <button onclick="performSearch()">搜索</button>
    </div>
    
    <div id="status"></div>
    <div id="results" class="results"></div>

    <script>
        // 离线搜索数据缓存
        let offlineData = [];
        let isDataLoaded = false;

        // 加载离线搜索数据
        const loadOfflineData = async () => {
            if (isDataLoaded) return;
            
            try {
                const response = await axios.get('/config/data.json');
                if (response.data && response.data.features) {
                    offlineData = response.data.features.map((feature, index) => ({
                        id: feature.id || index,
                        name: feature.properties.NAME || '',
                        type: feature.properties.LX || '',
                        telephone: feature.properties.TELEPHONE || '',
                        address: feature.properties.ADDRESS || '',
                        coordinates: feature.geometry.coordinates || [],
                        longitude: feature.properties.POINT_X || feature.geometry.coordinates[0],
                        latitude: feature.properties.POINT_Y || feature.geometry.coordinates[1],
                        _graphic: {
                            longitude: feature.properties.POINT_X || feature.geometry.coordinates[0],
                            latitude: feature.properties.POINT_Y || feature.geometry.coordinates[1]
                        }
                    }));
                    isDataLoaded = true;
                    showStatus(`离线搜索数据加载成功，共 ${offlineData.length} 条记录`, 'info');
                }
            } catch (error) {
                showStatus('加载离线搜索数据失败: ' + error.message, 'error');
                console.error('加载离线搜索数据失败:', error);
            }
        };

        // 离线搜索函数
        const offlineQueryData = async (keyword) => {
            await loadOfflineData();
            
            if (!keyword.trim()) {
                return { list: [] };
            }
            
            const filteredData = offlineData.filter(item => 
                item.name.toLowerCase().includes(keyword.toLowerCase()) ||
                item.type.toLowerCase().includes(keyword.toLowerCase()) ||
                item.address.toLowerCase().includes(keyword.toLowerCase())
            );
            
            return { list: filteredData };
        };

        // 显示状态信息
        const showStatus = (message, type = 'info') => {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        };

        // 显示搜索结果
        const displayResults = (results) => {
            const resultsDiv = document.getElementById('results');
            
            if (!results || results.length === 0) {
                resultsDiv.innerHTML = '<div class="no-results">未找到匹配的结果</div>';
                return;
            }

            const resultHtml = results.map(item => `
                <div class="result-item">
                    <div class="result-name">${item.name}</div>
                    <div class="result-type">类型: ${item.type}</div>
                    <div class="result-coords">坐标: ${item.longitude}, ${item.latitude}</div>
                    ${item.address ? `<div class="result-coords">地址: ${item.address}</div>` : ''}
                </div>
            `).join('');

            resultsDiv.innerHTML = resultHtml;
        };

        // 执行搜索
        const performSearch = async () => {
            const keyword = document.getElementById('searchInput').value;
            
            if (!keyword.trim()) {
                showStatus('请输入搜索关键词', 'error');
                return;
            }

            showStatus('搜索中...', 'loading');
            
            try {
                const result = await offlineQueryData(keyword);
                showStatus(`找到 ${result.list.length} 条结果`, 'info');
                displayResults(result.list);
            } catch (error) {
                showStatus('搜索失败: ' + error.message, 'error');
                console.error('搜索失败:', error);
            }
        };

        // 页面加载时自动加载数据
        window.addEventListener('load', () => {
            loadOfflineData();
        });

        // 支持回车键搜索
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    </script>
</body>
</html>
