import axios from "axios"
import { ref } from "vue"

/**
 * 离线搜索数据接口定义
 */
interface OfflineSearchItem {
  id: number
  name: string
  type: string
  telephone: string
  address: string
  coordinates: number[]
  longitude: number
  latitude: number
  lng: number  // 兼容原地图函数的经度字段
  lat: number  // 兼容原地图函数的纬度字段
  _graphic: {
    longitude: number
    latitude: number
  }
}

/**
 * 搜索结果接口定义
 */
interface SearchResult {
  list: OfflineSearchItem[]
  allcount?: number
}

// 使用 ref 缓存原始数据和处理后的数据
const rawDataCache = ref<any>(null)  // 缓存原始 JSON 数据
const offlineDataCache = ref<OfflineSearchItem[]>([])  // 缓存处理后的搜索数据

/**
 * 加载离线搜索数据
 * 从 public/config/data.json 加载 GeoJSON 格式的数据并转换为搜索格式
 * 使用 ref 缓存，避免重复请求
 */
const loadOfflineData = async (): Promise<void> => {
  // 如果已有缓存数据，直接返回
  if (offlineDataCache.value.length > 0) {
    console.log("使用缓存数据，共", offlineDataCache.value.length, "条记录")
    return
  }

  try {
    const response = await axios.get(`${import.meta.env.BASE_URL}config/POIall.json`)

    // 缓存原始数据
    rawDataCache.value = response.data

    if (response.data && response.data.features) {
      // 处理数据并缓存
      const processedData = response.data.features.map((feature: any, index: number): OfflineSearchItem => {
        const lng = feature.properties.POINT_X || feature.geometry.coordinates[0]
        const lat = feature.properties.POINT_Y || feature.geometry.coordinates[1]

        return {
          id: feature.id || index,
          name: feature.properties.NAME || "",
          type: feature.properties.LX || "",
          telephone: feature.properties.TELEPHONE || "",
          address: feature.properties.ADDRESS || "",
          coordinates: feature.geometry.coordinates || [],
          longitude: lng,
          latitude: lat,
          lng: lng,  // 兼容原地图函数
          lat: lat,  // 兼容原地图函数
          _graphic: {
            longitude: lng,
            latitude: lat
          }
        }
      })

      // 缓存处理后的数据
      offlineDataCache.value = processedData
      console.log("离线搜索数据加载成功，共", offlineDataCache.value.length, "条记录")
    }
  } catch (error) {
    console.error("加载离线搜索数据失败:", error)
    throw error
  }
}

/**
 * 离线搜索函数 - 用于搜索提示
 * 
 * @param keyword 搜索关键词
 * @returns 返回匹配的搜索结果，格式与原 mapWork.queryData 兼容
 */
export const offlineQueryData = async (keyword: string): Promise<SearchResult> => {
  await loadOfflineData()
  
  if (!keyword.trim()) {
    return { list: [] }
  }
  
  // 支持按名称、类型、地址进行模糊搜索
  const filteredData = offlineDataCache.value.filter((item: OfflineSearchItem) =>
    item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    item.type.toLowerCase().includes(keyword.toLowerCase()) ||
    item.address.toLowerCase().includes(keyword.toLowerCase())
  )
  
  return { list: filteredData }
}

/**
 * 离线搜索函数 - 用于详细搜索结果（无分页）
 * 替换 index.vue 中 querySiteList 函数里的 mapWork.querySiteList(text, page) 调用
 * 
 * @param keyword 搜索关键词
 * @returns 返回所有匹配的搜索结果，格式与原 mapWork.querySiteList 兼容
 */
export const offlineQuerySiteList = async (keyword: string): Promise<SearchResult> => {
  await loadOfflineData()
  
  if (!keyword.trim()) {
    return { list: [], allcount: 0 }
  }
  
  // 支持按名称、类型、地址进行模糊搜索
  const filteredData = offlineDataCache.value.filter((item: OfflineSearchItem) =>
    item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    item.type.toLowerCase().includes(keyword.toLowerCase()) ||
    item.address.toLowerCase().includes(keyword.toLowerCase())
  )
  
  return {
    list: filteredData,
    allcount: filteredData.length
  }
}

