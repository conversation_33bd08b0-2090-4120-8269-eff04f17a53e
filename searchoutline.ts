import axios from "axios"

/**
 * 离线搜索数据接口定义
 */
interface OfflineSearchItem {
  id: number
  name: string
  type: string
  telephone: string
  address: string
  coordinates: number[]
  longitude: number
  latitude: number
  _graphic: {
    longitude: number
    latitude: number
  }
}

/**
 * 搜索结果接口定义
 */
interface SearchResult {
  list: OfflineSearchItem[]
  allcount?: number
}

// 离线搜索数据缓存
let offlineData: OfflineSearchItem[] = []
let isDataLoaded = false

/**
 * 加载离线搜索数据
 * 从 public/config/data.json 加载 GeoJSON 格式的数据并转换为搜索格式
 */
const loadOfflineData = async (): Promise<void> => {
  if (isDataLoaded) return

  try {
    const response = await axios.get("/config/data.json")
    if (response.data && response.data.features) {
      offlineData = response.data.features.map((feature: any, index: number): OfflineSearchItem => ({
        id: feature.id || index,
        name: feature.properties.NAME || "",
        type: feature.properties.LX || "",
        telephone: feature.properties.TELEPHONE || "",
        address: feature.properties.ADDRESS || "",
        coordinates: feature.geometry.coordinates || [],
        longitude: feature.properties.POINT_X || feature.geometry.coordinates[0],
        latitude: feature.properties.POINT_Y || feature.geometry.coordinates[1],
        _graphic: {
          longitude: feature.properties.POINT_X || feature.geometry.coordinates[0],
          latitude: feature.properties.POINT_Y || feature.geometry.coordinates[1]
        }
      }))
      isDataLoaded = true
      console.log("离线搜索数据加载成功，共", offlineData.length, "条记录")
    }
  } catch (error) {
    console.error("加载离线搜索数据失败:", error)
    throw error
  }
}

/**
 * 离线搜索函数 - 用于搜索提示
 * 替换 index.vue 中 handleSearch 函数里的 mapWork.queryData(val) 调用
 * 
 * @param keyword 搜索关键词
 * @returns 返回匹配的搜索结果，格式与原 mapWork.queryData 兼容
 */
export const offlineQueryData = async (keyword: string): Promise<SearchResult> => {
  await loadOfflineData()
  
  if (!keyword.trim()) {
    return { list: [] }
  }
  
  // 支持按名称、类型、地址进行模糊搜索
  const filteredData = offlineData.filter(item => 
    item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    item.type.toLowerCase().includes(keyword.toLowerCase()) ||
    item.address.toLowerCase().includes(keyword.toLowerCase())
  )
  
  return { list: filteredData }
}

/**
 * 离线搜索函数 - 用于详细搜索结果（无分页）
 * 替换 index.vue 中 querySiteList 函数里的 mapWork.querySiteList(text, page) 调用
 * 
 * @param keyword 搜索关键词
 * @returns 返回所有匹配的搜索结果，格式与原 mapWork.querySiteList 兼容
 */
export const offlineQuerySiteList = async (keyword: string): Promise<SearchResult> => {
  await loadOfflineData()
  
  if (!keyword.trim()) {
    return { list: [], allcount: 0 }
  }
  
  // 支持按名称、类型、地址进行模糊搜索
  const filteredData = offlineData.filter(item => 
    item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    item.type.toLowerCase().includes(keyword.toLowerCase()) ||
    item.address.toLowerCase().includes(keyword.toLowerCase())
  )
  
  return {
    list: filteredData,
    allcount: filteredData.length
  }
}

/**
 * 清空图层函数 - 替换地图操作
 * 替换 index.vue 中 handleSearch 函数里的 mapWork.clearLayers() 调用
 */
export const clearLayers = (): void => {
  // 如果需要清空某些UI状态，可以在这里实现
  console.log("清空图层操作（离线模式）")
}

/**
 * 居中定位函数 - 替换地图操作
 * 替换 index.vue 中 handleSearch 函数里的 mapWork.centerAtLonLat(val) 调用
 *
 * @param coordinates 经纬度坐标字符串
 */
export const centerAtLonLat = (coordinates: string): void => {
  // 如果需要处理坐标定位，可以在这里实现
  console.log("定位到坐标:", coordinates)
}

/**
 * 显示POI数组函数 - 替换地图操作
 * 替换 index.vue 中 querySiteList 函数里的 mapWork.showPOIArr(result.list || []) 调用
 *
 * @param poiList POI点位数组
 */
export const showPOIArr = (poiList: OfflineSearchItem[]): void => {
  // 如果需要在地图上显示POI点位，可以在这里实现
  console.log("显示POI点位:", poiList.length, "个")
}

/**
 * 飞行到图形函数 - 替换地图操作
 * 替换 index.vue 中 flyTo 函数里的 mapWork.flyToGraphic(graphic, { radius: 2000 }) 调用
 *
 * @param graphic 图形对象
 * @param options 飞行选项
 */
export const flyToGraphic = (graphic: any, options: { radius: number }): void => {
  // 如果需要飞行到指定位置，可以在这里实现
  console.log("飞行到位置:", graphic, "选项:", options)
}
