import axios from "axios"

/**
 * 离线搜索数据接口定义
 */
interface OfflineSearchItem {
  id: number
  name: string
  type: string
  telephone: string
  address: string
  coordinates: number[]
  longitude: number
  latitude: number
  lng: number  // 兼容原地图函数的经度字段
  lat: number  // 兼容原地图函数的纬度字段
  _graphic: {
    longitude: number
    latitude: number
  }
}

/**
 * 搜索结果接口定义
 */
interface SearchResult {
  list: OfflineSearchItem[]
  allcount?: number
}

// 离线搜索数据缓存
let offlineData: OfflineSearchItem[] = []

/**
 * 加载离线搜索数据
 * 从 public/config/data.json 加载 GeoJSON 格式的数据并转换为搜索格式
 */
const loadOfflineData = async (): Promise<void> => {

  try {
    const response = await axios.get(`${import.meta.env.BASE_URL}config/POIall.json`)
    if (response.data && response.data.features) {
      offlineData = response.data.features.map((feature: any, index: number): OfflineSearchItem => {
        const lng = feature.properties.POINT_X || feature.geometry.coordinates[0]
        const lat = feature.properties.POINT_Y || feature.geometry.coordinates[1]

        return {
          id: feature.id || index,
          name: feature.properties.NAME || "",
          type: feature.properties.LX || "",
          telephone: feature.properties.TELEPHONE || "",
          address: feature.properties.ADDRESS || "",
          coordinates: feature.geometry.coordinates || [],
          longitude: lng,
          latitude: lat,
          lng: lng,  // 兼容原地图函数
          lat: lat,  // 兼容原地图函数
          _graphic: {
            longitude: lng,
            latitude: lat
          }
        }
      })
      console.log("离线搜索数据加载成功，共", offlineData.length, "条记录")
    }
  } catch (error) {
    console.error("加载离线搜索数据失败:", error)
    throw error
  }
}

/**
 * 离线搜索函数 - 用于搜索提示
 * 
 * @param keyword 搜索关键词
 * @returns 返回匹配的搜索结果，格式与原 mapWork.queryData 兼容
 */
export const offlineQueryData = async (keyword: string): Promise<SearchResult> => {
  await loadOfflineData()
  
  if (!keyword.trim()) {
    return { list: [] }
  }
  
  // 支持按名称、类型、地址进行模糊搜索
  const filteredData = offlineData.filter(item => 
    item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    item.type.toLowerCase().includes(keyword.toLowerCase()) ||
    item.address.toLowerCase().includes(keyword.toLowerCase())
  )
  
  return { list: filteredData }
}

/**
 * 离线搜索函数 - 用于详细搜索结果（无分页）
 * 替换 index.vue 中 querySiteList 函数里的 mapWork.querySiteList(text, page) 调用
 * 
 * @param keyword 搜索关键词
 * @returns 返回所有匹配的搜索结果，格式与原 mapWork.querySiteList 兼容
 */
export const offlineQuerySiteList = async (keyword: string): Promise<SearchResult> => {
  await loadOfflineData()
  
  if (!keyword.trim()) {
    return { list: [], allcount: 0 }
  }
  
  // 支持按名称、类型、地址进行模糊搜索
  const filteredData = offlineData.filter(item => 
    item.name.toLowerCase().includes(keyword.toLowerCase()) ||
    item.type.toLowerCase().includes(keyword.toLowerCase()) ||
    item.address.toLowerCase().includes(keyword.toLowerCase())
  )
  
  return {
    list: filteredData,
    allcount: filteredData.length
  }
}

